
import { db } from './db';
import { categories, games } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function getCategories() {
  return await db.query.categories.findMany({
    orderBy: (categories, { asc }) => [asc(categories.sortOrder)],
  });
}

export async function getGames() {
  return await db.query.games.findMany();
}

export async function getGamesByCategory(categoryId: number) {
  return await db.query.games.findMany({
    where: eq(games.categoryId, categoryId),
  });
}

export async function getGame(gameId: number) {
  return await db.query.games.findFirst({
    where: eq(games.id, gameId),
  });
}

export async function getCategory(categoryId: number) {
    return await db.query.categories.findFirst({
        where: eq(categories.id, categoryId),
    });
}
