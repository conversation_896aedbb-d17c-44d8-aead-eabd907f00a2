
import { db } from '@/lib/db';
import { categories, games } from '@/db/schema';

async function main() {
  // Seed categories
  const [action, adventure, puzzle, racing, sports] = await db.insert(categories).values([
    { name: 'Action', sortOrder: 1 },
    { name: 'Adventure', sortOrder: 2 },
    { name: 'Puzzle', sortOrder: 3 },
    { name: 'Racing', sortOrder: 4 },
    { name: 'Sports', sortOrder: 5 },
  ]).returning();

  // Seed games
  await db.insert(games).values([
    {
      categoryId: action.id,
      name: 'Galaxy Shooter',
      description: 'A classic space shooter game.',
      source: 'Open Source',
      coverImageUrl: 'https://placehold.co/400x400',
      gameUrl: 'https://www.addictinggames.com/embed/html5-games/23649',
      status: 1,
    },
    {
      categoryId: adventure.id,
      name: 'Jungle Run',
      description: 'An endless runner game through the jungle.',
      source: 'In-house',
      coverImageUrl: 'https://placehold.co/400x400',
      gameUrl: 'https://www.addictinggames.com/embed/html5-games/23649',
      status: 1,
    },
    {
      categoryId: puzzle.id,
      name: 'Su<PERSON>ku',
      description: 'A classic number puzzle game.',
      source: 'Partner',
      coverImageUrl: 'https://placehold.co/400x400',
      gameUrl: 'https://www.addictinggames.com/embed/html5-games/23649',
      status: 1,
    },
    {
        categoryId: racing.id,
        name: 'Asphalt 9',
        description: 'A classic racing game.',
        source: 'Partner',
        coverImageUrl: 'https://placehold.co/400x400',
        gameUrl: 'https://www.addictinggames.com/embed/html5-games/23649',
        status: 1,
      },
  ]);
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
});
