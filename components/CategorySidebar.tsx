
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Gamepad2, X } from 'lucide-react';
import { getCategories } from '@/lib/data';

interface CategorySidebarProps {
  categories: Awaited<ReturnType<typeof getCategories>>;
  isOpen: boolean;
  onClose: () => void;
}

export function CategorySidebar({ categories, isOpen, onClose }: CategorySidebarProps) {
  const pathname = usePathname();

  return (
    <aside 
      className={`bg-gray-800 text-white w-64 h-full p-4 transform transition-transform duration-300 ease-in-out ${isOpen ? 'translate-x-0' : 'translate-x-full'} md:translate-x-0 md:relative`}
    >
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">Categories</h2>
        <button onClick={onClose} className="md:hidden">
          <X />
        </button>
      </div>
      <nav>
        <ul>
          {categories.map((category) => {
            const isActive = pathname === `/category/${category.id}`;
            return (
              <li key={category.id} onClick={onClose}>
                <Link href={`/category/${category.id}`} className={`flex items-center p-2 rounded-lg hover:bg-gray-700 ${isActive ? 'bg-gray-700' : ''}`}>
                  <Gamepad2 className="mr-3" />
                  {category.name}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>
    </aside>
  );
}
