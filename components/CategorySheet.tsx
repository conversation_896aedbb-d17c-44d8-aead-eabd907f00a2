'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Gamepad2, X, Home } from 'lucide-react';
import { getCategories } from '@/lib/data';

interface CategorySheetProps {
  categories: Awaited<ReturnType<typeof getCategories>>;
  isOpen: boolean;
  onClose: () => void;
}

export function CategorySheet({ categories, isOpen, onClose }: CategorySheetProps) {
  const pathname = usePathname();

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"
          onClick={onClose}
        />
      )}

      {/* Sheet */}
      <div 
        className={`fixed inset-x-0 bottom-0 z-50 bg-gray-800 text-white rounded-t-2xl shadow-2xl transform transition-transform duration-300 ease-out ${
          isOpen ? 'translate-y-0' : 'translate-y-full'
        }`}
        style={{ maxHeight: '70vh' }}
      >
        {/* Handle */}
        <div className="flex justify-center pt-3 pb-2">
          <div className="w-12 h-1 bg-gray-600 rounded-full" />
        </div>

        {/* Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-gray-700">
          <h2 className="text-xl font-bold">Game Categories</h2>
          <button 
            onClick={onClose}
            className="p-2 hover:bg-gray-700 rounded-full transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="px-6 py-4 overflow-y-auto" style={{ maxHeight: 'calc(70vh - 120px)' }}>
          <nav>
            <ul className="space-y-2">
              {/* Home link */}
              <li>
                <Link 
                  href="/" 
                  onClick={onClose}
                  className={`flex items-center p-3 rounded-lg hover:bg-gray-700 transition-colors ${
                    pathname === '/' ? 'bg-purple-600 hover:bg-purple-700' : ''
                  }`}
                >
                  <Home className="mr-3 w-5 h-5" />
                  <span className="font-medium">All Games</span>
                </Link>
              </li>
              
              {/* Category links */}
              {categories.map((category) => {
                const isActive = pathname === `/category/${category.id}`;
                return (
                  <li key={category.id}>
                    <Link 
                      href={`/category/${category.id}`} 
                      onClick={onClose}
                      className={`flex items-center p-3 rounded-lg hover:bg-gray-700 transition-colors ${
                        isActive ? 'bg-purple-600 hover:bg-purple-700' : ''
                      }`}
                    >
                      <Gamepad2 className="mr-3 w-5 h-5" />
                      <span className="font-medium">{category.name}</span>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </nav>
        </div>
      </div>
    </>
  );
}
