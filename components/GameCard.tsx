
import Link from 'next/link';
import Image from 'next/image';

interface GameCardProps {
  game: {
    id: number;
    name: string;
    coverImageUrl: string | null;
  };
}

export function GameCard({ game }: GameCardProps) {
  return (
    <Link href={`/game/${game.id}`} className="group block bg-gray-800 rounded-lg overflow-hidden shadow-lg hover:scale-105 transition-transform duration-300">
      <div className="relative w-full h-48">
        {game.coverImageUrl && (
          <Image
            src={game.coverImageUrl}
            alt={game.name}
            layout="fill"
            objectFit="cover"
            className="transition-opacity duration-300 group-hover:opacity-90"
          />
        )}
      </div>
      <div className="p-4">
        <h3 className="text-lg font-semibold text-white truncate">{game.name}</h3>
      </div>
    </Link>
  );
}
