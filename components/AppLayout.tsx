
'use client';

import { useState } from 'react';
import { CategorySidebar } from "@/components/CategorySidebar";
import { Navbar } from "@/components/Navbar";
import { getCategories } from '@/lib/data';

export function AppLayout({
  children,
  categories,
}: {
  children: React.ReactNode;
  categories: Awaited<ReturnType<typeof getCategories>>;
}) {
  const [isSidebarOpen, setSidebarOpen] = useState(false);

  const handleClose = () => setSidebarOpen(false);

  return (
    <div className="flex h-screen bg-gray-900 text-white">
      <main className="flex-1 flex flex-col overflow-hidden">
        <Navbar onMenuClick={() => setSidebarOpen(true)} />
        <div className="flex-1 p-4 md:p-8 overflow-y-auto">
          {children}
        </div>
      </main>

      {/* Backdrop */}
      {isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
          onClick={handleClose}
        ></div>
      )}

      {/* Sidebar */}
      <div className={`fixed z-30 inset-y-0 right-0 md:relative md:z-auto md:block`}>
        <CategorySidebar categories={categories} isOpen={isSidebarOpen} onClose={handleClose} />
      </div>
    </div>
  );
}
