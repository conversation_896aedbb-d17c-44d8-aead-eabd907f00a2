
'use client';

import { useState } from 'react';
import { CategorySheet } from "@/components/CategorySheet";
import { Navbar } from "@/components/Navbar";
import { getCategories } from '@/lib/data';

export function AppLayout({
  children,
  categories,
}: {
  children: React.ReactNode;
  categories: Awaited<ReturnType<typeof getCategories>>;
}) {
  const [isSheetOpen, setSheetOpen] = useState(false);

  const handleClose = () => setSheetOpen(false);

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Navbar onMenuClick={() => setSheetOpen(true)} />
      <main className="p-4 md:p-8">
        {children}
      </main>

      {/* Category Sheet */}
      <CategorySheet
        categories={categories}
        isOpen={isSheetOpen}
        onClose={handleClose}
      />
    </div>
  );
}
