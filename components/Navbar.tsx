
'use client';

import Link from 'next/link';
import { Search, Menu, Swords } from 'lucide-react';

export function Navbar({ onMenuClick }: { onMenuClick: () => void }) {
  return (
    <header className="bg-gray-900 text-white shadow-md">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        <Link href="/" className="flex items-center space-x-2">
          <Swords className="text-purple-500" />
          <h1 className="text-xl font-bold">GameHub</h1>
        </Link>

        <div className="flex-1 mx-4 hidden md:block">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
            <input type="search" placeholder="Search..." className="bg-gray-800 text-white p-2 pl-10 rounded-full w-full focus:outline-none focus:ring-2 focus:ring-purple-500" />
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <button
            onClick={onMenuClick}
            className="flex items-center space-x-2 py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors"
          >
            <Menu className="w-5 h-5" />
            <span>Menu</span>
          </button>
        </div>
      </div>
    </header>
  );
}
