
'use client';

import Link from 'next/link';
import { Search, Heart, MenuIcon } from 'lucide-react';
import { CategorySheet } from './category-sheet';
import { getCategories } from '@/lib/data';

type Categories = Awaited<ReturnType<typeof getCategories>>;

export function Navbar({categories, onMenuClick }: {categories: Categories; onMenuClick: () => void }) {
  return (
    <header className="bg-black text-white shadow-md">
      <div className="mx-auto px-4 py-3 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <button onClick={onMenuClick} className='hidden lg:block cursor-pointer hover:bg-gray-800 rounded-full p-2'>
            <MenuIcon />
          </button>
          <div className='lg:hidden flex items-center'>
            <CategorySheet categories={categories}/>
          </div>
        <Link href="/" className="flex items-center space-x-2">
          <Heart className="text-purple-500" />
          <span className="text-xl font-bold">Love Tester</span>
        </Link>
        </div>

        <div className="mx-4 hidden md:block">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
            <input type="search" placeholder="Search..." className="bg-gray-800 text-white p-2 pl-10 rounded-full w-[500px] focus:outline-none focus:ring-2 focus:ring-purple-500" />
          </div>
        </div>

        <div className="flex items-center space-x-4">
          
        </div>
      </div>
    </header>
  );
}
