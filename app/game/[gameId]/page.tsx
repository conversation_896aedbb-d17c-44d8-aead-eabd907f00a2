
import { getGame } from '@/lib/data';

export default async function GamePage({ params }: { params: { gameId: string } }) {
  const gameId = parseInt(params.gameId, 10);
  const game = await getGame(gameId);

  if (!game) {
    return <div className="text-center text-xl">Game not found</div>;
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-gray-800 rounded-lg shadow-lg p-4 md:p-6">
        <h1 className="text-3xl font-bold mb-2 text-purple-400">{game.name}</h1>
        {game.source && <p className="text-sm text-gray-400 mb-4">Source: {game.source}</p>}
        <div className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden">
          <iframe src={game.gameUrl} className="w-full h-full border-0" allowFullScreen />
        </div>
      </div>
    </div>
  );
}
