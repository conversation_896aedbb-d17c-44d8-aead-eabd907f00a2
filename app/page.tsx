import { getGames } from '@/lib/data';
import { GameCard } from '@/components/GameCard';
import { Star } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { hostname } from 'os';

export default async function Home() {
  const games = await getGames();

  const hotGame = [
    {
      name:'dizzy love',
      coverImageUrl: '/small.gif',
      url:'/game/dizzy-love',
    },
    {
      name:'love test html',
      coverImageUrl: '/small.gif',
      url:'/game/love-test-html',
    },
    {
      name:'froggy love',
      coverImageUrl: '/small.gif',
      url:'/game/froggy-love',
    },
    {
      name:'love tester html5',
      coverImageUrl: '/small.gif',
      url:'/game/love-tester-html5',
    },
    {
      name:'real love tester',
      coverImageUrl: '/small.gif',
      url:'/game/real-love-tester',
    },
    {
      name:'love tester',
      coverImageUrl: '/small.gif',
      url:'/game/love-tester',
    },
    {
      name:'sweet love tester',
      coverImageUrl: '/small.gif',
      url:'/game/sweet-love-tester',
    },
    {
      name:'love tester fun love calculator',
      coverImageUrl: '/small.gif',
      url:'/game/love-tester-fun-love-calculator',
    },
    {
      name:'love tester deluxe',
      coverImageUrl: '/small.gif',
      url:'/game/love_tester_deluxe',
    },
    {
      name:'bratz love meter',
      coverImageUrl: '/small.gif',
      url:'/game/bratz-love-meter',
    },
  ]
  return (
    <>
    <div className='flex flex-wrap'>
      <div className=''>
        <div className='hidden md:block w-[830px] h-[620px] overflow-hidden'>
        <iframe src="https://y8.com/embed/pirate_love_tester/" scrolling="no" className='w-[800px] h-[600px] border-0 m-0 p-0'> </iframe>
        </div>
        <div className='md:hidden'>
          <iframe src="https://y8.com/embed/pirate_love_tester/" scrolling="no" className='w-full h-[200px] border-0 m-0 p-0'> </iframe>
        </div>
        <div className='flex gap-6'>
          <h1 className="text-3xl font-bold text-purple-400">Pirate Love Tester</h1>
          <p className='text-sm text-gray-400 flex items-end gap-3'><Star /> 9.4</p>
        </div>
      </div>
      <div className='grid grid-cols-3 gap-0 leading-none' style={{lineHeight: 0}}>
        {hotGame.map((game,i ) => (
          <div className='w-[180px] h-[135px] rounded-lg overflow-hidden bg-red-300 block leading-none' key={i} style={{margin: 0, padding: 0, lineHeight: 0}}>
            <Link href={game.url} className='block w-full h-full leading-none' style={{lineHeight: 0}}>
             {game.name}
            </Link>
          </div>
        ))}
      </div>
    </div>
    <div className='mt-10'>
      <h2 className='text-lg font-bold'>Game Description</h2>
      <div className='text-size:lg pt-8 leading-relaxed'>
        Set sail on a journey of love and adventure with Love Tester, a lighthearted game that reveals how well you and your crush match up!
Enter basic details like names, ages, heights, and eye colors, then let the system analyze your compatibility using its magical algorithm.

Choose between:

Simple Mode - Get instant results with a quick match!

Advanced Mode - Dive deeper with more inputs and see if the stars align.

Whether you're curious, crushing, or just having fun with friends, Love Tester is the perfect game to bring a spark of romance and a smile to your day.

Are you and your crush a perfect match? There is only one way to find out — play now!
      </div>
    </div>
    <div className='mt-8'>
      <div className='p-4 pl-0'>   
        <h2>Related Games</h2>
      </div>
      <div className="flex flex-wrap gap-6">
        {games.map((game) => (
          <GameCard key={game.id} game={game} />
        ))}
      </div>
    </div>
    <div className='mt-8'>
      <div className='p-4 pl-0'>
        <h2>Popluar Games</h2>
      </div>
      <div className="flex flex-wrap gap-6">
        {games.map((game) => (
          <GameCard key={game.id} game={game} />
        ))}
      </div>
    </div>
    </>
  );
}

