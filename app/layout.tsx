
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AppLayout } from "@/components/AppLayout";
import { getCategories } from "@/lib/data";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "GameHub",
  description: "An online H5 game hub",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const categories = await getCategories();

  return (
    <html lang="en">
      <body className={`${inter.className} bg-gray-900 text-white`}>
        <AppLayout categories={categories}>
          {children}
        </AppLayout>
      </body>
    </html>
  );
}

