
import { getGamesByCategory, getCategory } from '@/lib/data';
import { GameCard } from '@/components/GameCard';

export default async function CategoryPage({ params }: { params: { categoryId: string } }) {
  const categoryId = parseInt(params.categoryId, 10);
  const games = await getGamesByCategory(categoryId);
  const category = await getCategory(categoryId);

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6 text-purple-400">{category?.name}</h1>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {games.map((game) => (
          <GameCard key={game.id} game={game} />
        ))}
      </div>
    </div>
  );
}
